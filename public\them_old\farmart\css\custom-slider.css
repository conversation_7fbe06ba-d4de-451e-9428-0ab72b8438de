/* Custom CSS to handle slider dimensions and spacing */

/* Base slider container */
.section-content.section-content__slider {
    background-image: none !important;
    background-color: #ffffff !important;
    padding-top: 10px !important; /* Top gap from header */
    padding-bottom: 14px !important; /* Desired gap to next element */
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    width: 1090px !important; /* Fixed width */
    height: 384px !important; /* Fixed height */
    max-width: 1090px !important;
    max-height: 384px !important;
}

/* Ensure background is completely removed */
.section-content.section-content__slider.lazyload,
.section-content.section-content__slider.lazyload[data-bg] {
    background-image: none !important;
    background: none !important;
}

/* Slider wrapper */
.section-content__slider .section-slides-wrapper {
    border-radius: 10px;
    overflow: visible !important;
    position: relative;
    width: 100% !important;
    height: 100% !important;
    margin: 0 !important;
}

/* Slide items and slick carousel init */
.section-content__slider .section-slides-wrapper .slide-item .slide-item__image,
.section-content__slider .section-slides-wrapper .slick-slides-carousel:not(.slick-initialized) {
    height: 384px !important;
    width: 1090px !important;
    max-height: 384px !important;
    max-width: 1090px !important;
    background-color: #d6dee5; /* Placeholder background */
}

/* Image handling within slides */
.section-content__slider .section-slides-wrapper .slide-item .slide-item__image img,
.section-content__slider .section-slides-wrapper .slide-item .slide-item__image picture,
.section-content__slider .section-slides-wrapper .slide-item .slide-item__image source {
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    object-fit: cover !important; /* Ensures image covers the area */
    display: block !important;
}

/* Ad banner beside slider (if applicable) */
.section-banner-wrapper .banner-item__image {
    height: 384px !important;
    width: 1090px !important;
    max-height: 384px !important;
    max-width: 1090px !important;
    background-color: #d6dee5; /* Placeholder background */
}

.section-banner-wrapper .banner-item__image img,
.section-banner-wrapper .banner-item__image a,
.section-banner-wrapper .banner-item__image picture {
    height: 100% !important;
    width: 100% !important;
    object-fit: cover !important;
    display: block !important;
}

/* Spacing for the section directly following the slider */
.widget-product-categories {
    padding-top: 14px !important; /* Desired gap from slider */
    margin-top: 0 !important;
}

/* Override any padding in the first section after slider, targeting specific child if necessary */
.section-content__slider + div .widget-product-categories,
#main-content > div > div > div:nth-child(2) {
    padding-top: 14px !important;
    margin-top: 0 !important;
}

/* Mobile adjustments */
@media (max-width: 767px) {
    .section-content.section-content__slider {
        width: 100% !important;
        height: auto !important; /* Auto height for responsiveness */
        max-width: 100% !important;
        padding: 5px 0 !important; /* Smaller padding on mobile */
    }

    .section-content__slider .section-slides-wrapper .slide-item .slide-item__image,
    .section-content__slider .section-slides-wrapper .slick-slides-carousel:not(.slick-initialized) {
        height: 45vw !important; /* Responsive height for mobile images */
        width: 100% !important;
        max-height: none !important;
        max-width: 100% !important;
    }

    .section-banner-wrapper .banner-item__image {
        height: 45vw !important; /* Responsive height for mobile ad images */
        width: 100% !important;
        max-height: none !important;
        max-width: 100% !important;
    }
}
