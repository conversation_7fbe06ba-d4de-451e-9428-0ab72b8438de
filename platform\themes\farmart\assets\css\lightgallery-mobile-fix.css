/* LightGallery Mobile Fixes - High Specificity Override */

/* Fix for mobile image viewer header controls being cut off */
@media (max-width: 992px) {
    /* Force lightGallery container to proper positioning with highest specificity */
    body .lg-outer,
    html body .lg-outer {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 99999 !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Force counter to be visible at top left */
    body .lg-outer .lg-counter,
    html body .lg-outer .lg-counter {
        position: fixed !important;
        top: 15px !important;
        left: 15px !important;
        color: #fff !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        z-index: 100001 !important;
        background: rgba(0, 0, 0, 0.7) !important;
        padding: 8px 12px !important;
        border-radius: 20px !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5) !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Force close button to be visible at top right */
    body .lg-outer .lg-close,
    html body .lg-outer .lg-close {
        position: fixed !important;
        top: 15px !important;
        right: 15px !important;
        width: 40px !important;
        height: 40px !important;
        background: rgba(0, 0, 0, 0.7) !important;
        border-radius: 50% !important;
        color: #fff !important;
        font-size: 20px !important;
        cursor: pointer !important;
        z-index: 100001 !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Force download button to be visible */
    body .lg-outer .lg-download,
    html body .lg-outer .lg-download {
        position: fixed !important;
        top: 15px !important;
        right: 65px !important;
        width: 40px !important;
        height: 40px !important;
        background: rgba(0, 0, 0, 0.7) !important;
        border-radius: 50% !important;
        color: #fff !important;
        font-size: 18px !important;
        cursor: pointer !important;
        z-index: 100001 !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    /* Hover effects for buttons */
    body .lg-outer .lg-close:hover,
    html body .lg-outer .lg-close:hover,
    body .lg-outer .lg-download:hover,
    html body .lg-outer .lg-download:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        transform: scale(1.05) !important;
        transition: all 0.2s ease !important;
    }

    /* Ensure main image area doesn't overlap with controls */
    body .lg-outer .lg-item,
    html body .lg-outer .lg-item {
        padding-top: 70px !important;
        height: calc(100vh - 70px) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-sizing: border-box !important;
    }

    /* Ensure images are properly contained */
    body .lg-outer .lg-item img,
    html body .lg-outer .lg-item img {
        max-width: 100% !important;
        max-height: 100% !important;
        object-fit: contain !important;
        display: block !important;
    }

    /* Fix thumbnail area if present */
    body .lg-outer .lg-thumb-outer,
    html body .lg-outer .lg-thumb-outer {
        bottom: 0 !important;
        height: 80px !important;
        background: rgba(0, 0, 0, 0.8) !important;
        z-index: 100000 !important;
    }

    /* Adjust main content when thumbnails are open */
    body .lg-outer.lg-thumb-open .lg-item,
    html body .lg-outer.lg-thumb-open .lg-item {
        height: calc(100vh - 150px) !important;
    }

    /* Ensure backdrop is properly styled */
    body .lg-backdrop,
    html body .lg-backdrop {
        background: rgba(0, 0, 0, 0.95) !important;
        z-index: 99998 !important;
    }
}

/* Additional fixes for very small screens */
@media (max-width: 480px) {
    /* Smaller buttons and text for very small screens */
    body .lg-outer .lg-counter,
    html body .lg-outer .lg-counter {
        top: 10px !important;
        left: 10px !important;
        font-size: 14px !important;
        padding: 6px 10px !important;
    }

    body .lg-outer .lg-close,
    html body .lg-outer .lg-close {
        top: 10px !important;
        right: 10px !important;
        width: 36px !important;
        height: 36px !important;
        font-size: 18px !important;
    }

    body .lg-outer .lg-download,
    html body .lg-outer .lg-download {
        top: 10px !important;
        right: 55px !important;
        width: 36px !important;
        height: 36px !important;
        font-size: 16px !important;
    }

    body .lg-outer .lg-item,
    html body .lg-outer .lg-item {
        padding-top: 60px !important;
        height: calc(100vh - 60px) !important;
    }

    body .lg-outer.lg-thumb-open .lg-item,
    html body .lg-outer.lg-thumb-open .lg-item {
        height: calc(100vh - 140px) !important;
    }
}
