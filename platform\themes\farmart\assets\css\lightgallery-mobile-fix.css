/* LightGallery Mobile Fixes */

/* Fix for mobile image viewer header controls being cut off */
@media (max-width: 992px) {
    /* Ensure lightGallery container has proper positioning */
    .lg-outer {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        z-index: 9999 !important;
    }

    /* Fix header toolbar positioning - LightGallery 1.6.9 uses different structure */
    .lg-toolbar {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        height: 50px !important;
        background: rgba(0, 0, 0, 0.8) !important;
        z-index: 10001 !important;
        padding: 10px 15px !important;
        box-sizing: border-box !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
    }

    /* Alternative toolbar structure for different lightGallery versions */
    .lg-outer .lg-toolbar {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 50px !important;
        background: rgba(0, 0, 0, 0.8) !important;
        z-index: 10001 !important;
        padding: 10px 15px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
    }
    
    /* Fix image counter positioning */
    .lg-counter {
        position: relative !important;
        top: auto !important;
        left: auto !important;
        color: #fff !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        z-index: 10002 !important;
        order: 1 !important;
    }

    /* Alternative counter positioning for different versions */
    .lg-outer .lg-counter {
        position: absolute !important;
        top: 15px !important;
        left: 15px !important;
        color: #fff !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        z-index: 10002 !important;
        background: rgba(0, 0, 0, 0.5) !important;
        padding: 5px 10px !important;
        border-radius: 15px !important;
    }

    /* Fix action buttons positioning */
    .lg-actions {
        position: relative !important;
        top: auto !important;
        right: auto !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
        z-index: 10002 !important;
        order: 2 !important;
    }

    /* Alternative actions positioning */
    .lg-outer .lg-actions {
        position: absolute !important;
        top: 10px !important;
        right: 15px !important;
        display: flex !important;
        align-items: center !important;
        gap: 10px !important;
        z-index: 10002 !important;
    }
    
    /* Style individual action buttons */
    .lg-actions .lg-icon {
        width: 36px !important;
        height: 36px !important;
        background: rgba(255, 255, 255, 0.2) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: #fff !important;
        font-size: 16px !important;
        cursor: pointer !important;
        transition: background-color 0.3s ease !important;
    }
    
    .lg-actions .lg-icon:hover {
        background: rgba(255, 255, 255, 0.3) !important;
    }
    
    /* Fix close button */
    .lg-close {
        position: relative !important;
        top: auto !important;
        right: auto !important;
        width: 36px !important;
        height: 36px !important;
        background: rgba(255, 255, 255, 0.2) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: #fff !important;
        font-size: 18px !important;
        cursor: pointer !important;
        z-index: 10002 !important;
    }

    /* Alternative close button positioning */
    .lg-outer .lg-close {
        position: absolute !important;
        top: 10px !important;
        right: 10px !important;
        width: 36px !important;
        height: 36px !important;
        background: rgba(0, 0, 0, 0.6) !important;
        border-radius: 50% !important;
        color: #fff !important;
        font-size: 18px !important;
        cursor: pointer !important;
        z-index: 10002 !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
    }

    .lg-close:hover,
    .lg-outer .lg-close:hover {
        background: rgba(255, 255, 255, 0.3) !important;
    }

    /* Fix download button */
    .lg-download {
        position: relative !important;
        top: auto !important;
        right: auto !important;
    }

    /* Alternative download button positioning */
    .lg-outer .lg-download {
        position: absolute !important;
        top: 10px !important;
        right: 55px !important;
        width: 36px !important;
        height: 36px !important;
        background: rgba(0, 0, 0, 0.6) !important;
        border-radius: 50% !important;
        color: #fff !important;
        font-size: 16px !important;
        cursor: pointer !important;
        z-index: 10002 !important;
        border: 2px solid rgba(255, 255, 255, 0.3) !important;
    }
    
    /* Adjust main image container to account for header */
    .lg-item {
        padding-top: 50px !important;
        height: calc(100% - 50px) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
    
    /* Ensure images are properly sized */
    .lg-item img {
        max-width: 100% !important;
        max-height: 100% !important;
        object-fit: contain !important;
    }
    
    /* Fix thumbnail navigation if present */
    .lg-thumb-outer {
        bottom: 0 !important;
        height: 80px !important;
        background: rgba(0, 0, 0, 0.8) !important;
    }
    
    /* Adjust main content area when thumbnails are present */
    .lg-thumb-open .lg-item {
        height: calc(100% - 130px) !important;
    }
    
    /* Fix for any overlay elements */
    .lg-backdrop {
        background: rgba(0, 0, 0, 0.9) !important;
    }
    
    /* Ensure proper touch/swipe area */
    .lg-item .lg-img-wrap {
        width: 100% !important;
        height: 100% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }
}

/* Additional fixes for very small screens */
@media (max-width: 480px) {
    .lg-toolbar {
        height: 45px !important;
        padding: 8px 12px !important;
    }
    
    .lg-counter {
        font-size: 12px !important;
    }
    
    .lg-actions .lg-icon,
    .lg-close {
        width: 32px !important;
        height: 32px !important;
        font-size: 14px !important;
    }
    
    .lg-item {
        padding-top: 45px !important;
        height: calc(100% - 45px) !important;
    }
    
    .lg-thumb-open .lg-item {
        height: calc(100% - 125px) !important;
    }
}
