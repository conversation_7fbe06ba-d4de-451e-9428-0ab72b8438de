<div class="widget-products-with-category py-5 bg-light">
    <div class="container-xxxl">
        <div class="row">
            <div class="col-12">
                @if (!empty($title))
                    <div class="text-center mb-5">
                        <h2 class="section-title">{{ $title }}</h2>
                    </div>
                @endif
                <div class="product-grid-wrapper">
                    <div class="products-grid">
                        @foreach ($products as $product)
                            <div class="product-inner bg-white">
                                {!! Theme::partial('ecommerce.product-item-grid', compact('product', 'wishlistIds')) !!}
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .section-title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 0;
        color: #000;
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 1rem;
    }

    @media (max-width: 1400px) {
        .products-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    @media (max-width: 1199px) {
        .products-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (max-width: 1024px) {
        .products-grid {
            grid-template-columns: repeat(3, 1fr);
        }
        .section-title {
            font-size: 28px;
        }
    }

    @media (max-width: 767px) {
        .products-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }
        .section-title {
            font-size: 24px;
        }
    }

    .product-inner {
        width: 100%;
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #eee;
        transition: box-shadow 0.3s ease;
        display: flex;
        flex-direction: column;
    }

    .product-inner:hover {
        box-shadow: 0 1px 6px rgba(0,0,0,0.1);
    }

    .product-inner .product-thumbnail {
        position: relative;
        margin: 0;
        padding: 0;
        line-height: 0;
    }

    .product-inner .product-thumbnail img {
        width: 100%;
        height: auto;
        display: block;
    }

    .product-inner .product-details {
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
    }

    .product-inner .product-price-wrapper {
        margin: 0;
        width: 100%;
        background-color: #ff5b31;
        padding: 8px 10px;
        color: #fff;
    }

    .product-inner .product-content-box {
        padding: 10px;
    }
</style> 